<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钱币预览页面 - 移动端测试</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft Yahei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B0000;
            text-align: center;
            margin-bottom: 30px;
            font-family: 'STKaiti', 'KaiTi', serif;
            letter-spacing: 2px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #E8E8E8;
            border-radius: 8px;
            position: relative;
        }
        .test-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #8B0000 0%, #DC143C 50%, #8B0000 100%);
            border-radius: 8px 8px 0 0;
        }
        .test-section h3 {
            color: #8B0000;
            margin-top: 8px;
            margin-bottom: 15px;
            font-weight: 700;
        }
        .device-frame {
            border: 3px solid #333;
            border-radius: 20px;
            padding: 10px;
            background: #000;
            margin: 20px auto;
            position: relative;
        }
        .mobile-frame {
            width: 375px;
            height: 667px;
        }
        .tablet-frame {
            width: 768px;
            height: 1024px;
        }
        .desktop-frame {
            width: 100%;
            height: 600px;
            border-radius: 8px;
        }
        .device-screen {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 15px;
            background: white;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .test-link {
            display: block;
            padding: 15px 20px;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            letter-spacing: 1px;
        }
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(139, 0, 0, 0.3);
        }
        .features {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .features h4 {
            color: #8B0000;
            margin-bottom: 15px;
            font-weight: 700;
        }
        .features ul {
            list-style: none;
            padding: 0;
        }
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            position: relative;
            padding-left: 20px;
        }
        .features li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #8B0000;
            font-weight: bold;
        }
        .features li:last-child {
            border-bottom: none;
        }
        .responsive-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #8B0000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .info-card h5 {
            color: #8B0000;
            margin-bottom: 10px;
            font-weight: 700;
        }
        .info-card p {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            .mobile-frame, .tablet-frame {
                width: 100%;
                max-width: 375px;
                height: 500px;
            }
            .test-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 钱币评级预览页面 - 中式风格重构</h1>
        
        <div class="test-section">
            <h3>📱 移动端适配测试</h3>
            <p>重新设计的钱币详情页面采用了中国传统风格，优化了移动端体验。</p>
            
            <div class="test-links">
                <a href="http://localhost:5174/coin-preview/ZK12000276" target="_blank" class="test-link">
                    🖥️ 桌面端预览
                </a>
                <a href="http://localhost:5174/coin-preview/ZK12000276" target="_blank" class="test-link">
                    📱 移动端预览
                </a>
                <a href="test-coin-preview.html" target="_blank" class="test-link">
                    🔧 API 测试工具
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>🎨 设计特色</h3>
            <div class="features">
                <h4>中式传统风格元素：</h4>
                <ul>
                    <li>采用中国红配色方案 (#8B0000, #DC143C)</li>
                    <li>使用楷体字体显示标题，体现传统文化</li>
                    <li>金色装饰元素，突出评级权威性</li>
                    <li>传统印章风格的装饰图案</li>
                    <li>简洁而庄重的卡片式布局</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>📱 响应式设计优化</h3>
            <div class="responsive-info">
                <div class="info-card">
                    <h5>桌面端 (>768px)</h5>
                    <p>左右分栏布局，图片和信息并排显示，充分利用屏幕空间，提供最佳的视觉体验。</p>
                </div>
                <div class="info-card">
                    <h5>平板端 (768px)</h5>
                    <p>自动调整为单列布局，保持良好的可读性和交互体验，图片适当缩放。</p>
                </div>
                <div class="info-card">
                    <h5>手机端 (<480px)</h5>
                    <p>优化触摸操作，增大按钮尺寸，调整字体大小，确保在小屏幕上的可用性。</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 技术实现</h3>
            <div class="features">
                <h4>主要改进：</h4>
                <ul>
                    <li>CSS Grid 和 Flexbox 实现响应式布局</li>
                    <li>优化的字体层级和间距设计</li>
                    <li>流畅的动画和过渡效果</li>
                    <li>移动端友好的触摸交互</li>
                    <li>无障碍访问支持</li>
                    <li>兼容各种屏幕尺寸和设备</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 测试建议</h3>
            <div class="features">
                <h4>请在以下环境中测试：</h4>
                <ul>
                    <li>Chrome/Safari 桌面浏览器</li>
                    <li>移动设备浏览器 (iOS Safari, Android Chrome)</li>
                    <li>不同屏幕尺寸 (手机、平板、桌面)</li>
                    <li>横屏和竖屏模式</li>
                    <li>触摸和鼠标交互</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #8B0000;">
            <p style="color: #8B0000; font-weight: 600; letter-spacing: 1px;">
                © 2024 中乾评级 - 专业钱币评级认证服务
            </p>
        </div>
    </div>

    <script>
        // 检测设备类型
        function detectDevice() {
            const width = window.innerWidth;
            if (width <= 480) return 'mobile';
            if (width <= 768) return 'tablet';
            return 'desktop';
        }

        // 显示当前设备信息
        function showDeviceInfo() {
            const device = detectDevice();
            const deviceMap = {
                'mobile': '📱 手机端',
                'tablet': '📱 平板端', 
                'desktop': '🖥️ 桌面端'
            };
            
            console.log(`当前设备: ${deviceMap[device]} (${window.innerWidth}px)`);
        }

        // 页面加载完成后显示设备信息
        window.addEventListener('load', showDeviceInfo);
        window.addEventListener('resize', showDeviceInfo);

        // 添加测试链接点击统计
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log(`点击测试链接: ${this.textContent.trim()}`);
            });
        });
    </script>
</body>
</html>
