<template>
  <!-- 增强的打印标签预览抽屉 -->
  <el-drawer
    v-model="visible"
    title=""
    direction="rtl"
    size="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="enhanced-preview-drawer"
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <div class="drawer-title-section">
          <span class="drawer-title">打印标签预览</span>
          <el-tag v-if="printData?.templateName" type="info" size="small">
            {{ printData.templateName }}
          </el-tag>
        </div>
        <div class="drawer-actions">
          <el-button-group>
            <el-button
              :icon="ZoomOut"
              @click="handleZoomOut"
              size="small"
              :disabled="scale <= minScale"
            />
            <el-button size="small" style="min-width: 60px">
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button
              :icon="ZoomIn"
              @click="handleZoomIn"
              size="small"
              :disabled="scale >= maxScale"
            />
          </el-button-group>
          <el-button :icon="RefreshRight" @click="handleRefresh" size="small">
            刷新
          </el-button>
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            type="info"
            :icon="View"
            @click="handlePrintPreview"
            size="small"
          >
            打印预览
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <!-- 信息栏 -->
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 预览区域 -->
      <div class="preview-area" :style="previewAreaStyle">
        <!-- 分页展示所有打印标签 -->
        <div
          v-for="(page, pageIndex) in paginatedTemplates"
          :key="`page-${pageIndex}`"
          class="print-page"
          :style="getPrintPageStyle(pageIndex)"
        >
          <!-- 页面标题 -->
          <!--          <div class="page-header">
            <span class="page-number">第 {{ pageIndex + 1 }} 页</span>
            <span class="page-info">共 {{ page.length }} 个标签</span>
          </div>-->

          <!-- 页面内的标签列表 -->
          <div class="labels-container">
            <div
              v-for="(item, labelIndex) in page"
              :key="`page-${pageIndex}-label-${labelIndex}`"
              class="label-item"
              :style="getLabelItemStyle(labelIndex)"
            >
              <!-- hiprint模板渲染容器 -->
              <div
                :id="`hiprint-preview-container-${item.originalIndex}`"
                class="hiprint-preview-container"
                v-loading="renderLoading && item.originalIndex === 0"
              >
                <!-- hiprint渲染的HTML内容将在这里显示 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, nextTick, onMounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    Printer,
    Download,
    ZoomIn,
    ZoomOut,
    RefreshRight,
    View
  } from '@element-plus/icons-vue';

  // 导入hiprint配置工具
  import {
    initHiprint as initHiprintPlugin,
    createPrintTemplate,
    getHiprintInstance,
    handleHiprintError
  } from '@/utils/hiprint-config';

  // hiprint实例
  let hiprintTemplate = null;

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    printData: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['update:modelValue', 'confirm-print', 'refresh']);

  // 响应式数据
  const loading = ref(false);
  const renderLoading = ref(false);
  const printing = ref(false);
  const downloading = ref(false);
  const scale = ref(1);
  const minScale = 0.5;
  const maxScale = 2;

  // 分页配置
  const pageConfig = ref({
    maxHeight: 800, // 页面最大高度 (mm转px，大约A4纸高度)
    pageWidth: 595, // 页面宽度 (mm转px，大约A4纸宽度)
    labelSpacing: 10, // 标签间距 (px)
    pageMargin: 40, // 页面边距 (px)
    estimatedLabelHeight: 80 // 预估标签高度 (px)
  });

  // 纸张设置信息
  const paperSettings = ref(null);

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  const previewAreaStyle = computed(() => ({
    transform: `scale(${scale.value})`,
    transformOrigin: 'center top',
    transition: 'transform 0.3s ease',
    padding: '20px'
  }));

  // 分页模板数据 - 修改为使用数据项而不是处理后的模板
  const paginatedTemplates = computed(() => {
    if (!props.printData?.items?.length) {
      return [];
    }

    const items = props.printData.items.map((item, index) => ({
      ...item,
      originalIndex: index
    }));

    // 根据标签高度进行分页
    const pages = [];
    let currentPage = [];
    let currentPageHeight = pageConfig.value.pageMargin * 2; // 初始页面边距

    items.forEach((item, index) => {
      const estimatedHeight =
        pageConfig.value.estimatedLabelHeight + pageConfig.value.labelSpacing;

      // 检查是否需要换页
      if (
        currentPageHeight + estimatedHeight > pageConfig.value.maxHeight &&
        currentPage.length > 0
      ) {
        pages.push([...currentPage]);
        currentPage = [];
        currentPageHeight = pageConfig.value.pageMargin * 2;
      }

      currentPage.push(item);
      currentPageHeight += estimatedHeight;
    });

    // 添加最后一页
    if (currentPage.length > 0) {
      pages.push(currentPage);
    }

    return pages;
  });

  // 获取单个打印页面样式（带间距效果）
  const getPrintPageStyle = (pageIndex) => {
    // 计算打印底纸尺寸
    const paperWidth = paperSettings.value?.paperWidth || 210; // 默认A4宽度(mm)
    const paperHeight = paperSettings.value?.paperHeight || 297; // 默认A4高度(mm)

    return {
      background: 'white',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      padding: `${pageConfig.value.pageMargin}px`,
      margin: pageIndex === 0 ? '0 auto 30px auto' : '30px auto',
      width: `${paperWidth}mm`,
      minHeight: `${paperHeight}mm`,
      overflow: 'hidden',
      position: 'relative'
    };
  };

  // 获取标签项样式
  const getLabelItemStyle = (labelIndex) => ({
    marginBottom: labelIndex === 0 ? '0' : `${pageConfig.value.labelSpacing}px`,
    position: 'relative'
  });

  // 解析打印底纸设置
  const parsePaperSettings = () => {
    if (props.printData?.pageSettings) {
      try {
        paperSettings.value = JSON.parse(props.printData.pageSettings);
        console.log('解析打印底纸设置成功:', paperSettings.value);

        // 更新页面配置 - 使用打印底纸的大小
        if (
          paperSettings.value?.paperWidth &&
          paperSettings.value?.paperHeight
        ) {
          const { paperWidth, paperHeight } = paperSettings.value;
          // 将mm转换为px (大约1mm = 3.78px)
          pageConfig.value.pageWidth = paperWidth * 3.78;
          pageConfig.value.maxHeight = paperHeight * 3.78;
          console.log(`设置打印底纸尺寸: ${paperWidth}×${paperHeight}mm`);
        }
      } catch (error) {
        console.warn('解析打印底纸设置失败:', error);
        paperSettings.value = null;
      }
    }
  };

  // 初始化hiprint
  const initHiprint = async () => {
    try {
      // 使用工具模块初始化hiprint
      await initHiprintPlugin();
      console.log('hiprint初始化成功');
    } catch (error) {
      console.error('初始化hiprint失败:', error);
      throw error;
    }
  };

  // 渲染预览 - 修改为正确的 hiprint 数据绑定方式
  const renderPreview = async () => {
    if (!props.printData?.items?.length || !props.printData?.layoutConfig) {
      throw new Error('没有可预览的数据或模板配置');
    }

    renderLoading.value = true;

    try {
      // 解析纸张设置
      parsePaperSettings();

      // 确保hiprint已初始化
      await initHiprint();

      // 使用原始模板配置创建一个hiprint模板实例
      const templateConfig = props.printData.layoutConfig;
      if (!templateConfig?.panels?.[0]) {
        throw new Error('模板配置无效');
      }

      // 创建hiprint模板实例（使用原始模板，不是处理后的模板）
      const hiprintTemplate = createPrintTemplate(templateConfig, {
        dataMode: 1
      });

      // 为每个数据项渲染HTML
      for (let i = 0; i < props.printData.items.length; i++) {
        const itemData = props.printData.items[i];

        await nextTick();
        const container = document.getElementById(
          `hiprint-preview-container-${i}`
        );
        if (container && hiprintTemplate) {
          // 使用 hiprint 的 getHtml 方法，传入数据对象让 hiprint 自动绑定
          const htmlContent = hiprintTemplate.getHtml(itemData);

          if (htmlContent && htmlContent.length > 0) {
            // 清空容器并添加新内容
            container.innerHTML = '';
            if (typeof htmlContent === 'string') {
              container.innerHTML = htmlContent;
            } else if (htmlContent[0]) {
              // 如果是jQuery对象或DOM元素数组
              if (htmlContent[0].innerHTML) {
                container.innerHTML = htmlContent[0].innerHTML;
              } else {
                container.appendChild(htmlContent[0]);
              }
            }

            // 渲染完成后，动态调整页面高度配置
            await nextTick();
            adjustPageConfigBasedOnActualHeight(container, i);
          } else {
            throw new Error(`数据项 ${i} 获取的HTML内容为空`);
          }
        } else {
          throw new Error(`数据项 ${i} 预览容器不存在`);
        }
      }
    } catch (error) {
      console.error('渲染预览失败:', error);
      const errorMessage = handleHiprintError
        ? handleHiprintError(error)
        : error.message;

      // 在第一个容器显示错误信息
      const container = document.getElementById('hiprint-preview-container-0');
      if (container) {
        container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #999;">
          <p>预览渲染失败</p>
          <p style="font-size: 12px; margin-top: 8px;">${errorMessage}</p>
        </div>
      `;
      }
      throw error;
    } finally {
      renderLoading.value = false;
    }
  };

  // 根据实际渲染高度调整页面配置
  const adjustPageConfigBasedOnActualHeight = (container, index) => {
    if (container && index === 0) {
      // 只在第一个标签渲染完成后调整
      const actualHeight = container.offsetHeight;
      if (
        actualHeight > 0 &&
        actualHeight !== pageConfig.value.estimatedLabelHeight
      ) {
        pageConfig.value.estimatedLabelHeight = Math.max(actualHeight, 60); // 最小高度60px
        console.log(
          `调整标签预估高度为: ${pageConfig.value.estimatedLabelHeight}px`
        );
      }
    }
  };

  // 获取hiprint相关的CSS样式
  const getHiprintStyles = () => {
    const hiprintStyles = [];

    try {
      // 查找包含hiprint相关样式的样式表
      Array.from(document.styleSheets).forEach((styleSheet) => {
        try {
          Array.from(styleSheet.cssRules).forEach((rule) => {
            const cssText = rule.cssText;
            // 匹配hiprint相关的CSS规则
            if (
              cssText.includes('hiprint') ||
              cssText.includes('.printPanel') ||
              cssText.includes('.printElement') ||
              cssText.includes('print-page') ||
              cssText.includes('label-item')
            ) {
              hiprintStyles.push(cssText);
            }
          });
        } catch (e) {
          // 跨域样式表无法访问，忽略错误
          console.warn('无法访问样式表:', e.message);
        }
      });
    } catch (error) {
      console.warn('获取hiprint样式失败:', error);
    }

    return hiprintStyles.join('\n');
  };

  const handleZoomIn = () => {
    if (scale.value < maxScale) {
      scale.value = Math.min(scale.value + 0.1, maxScale);
    }
  };

  const handleZoomOut = () => {
    if (scale.value > minScale) {
      scale.value = Math.max(scale.value - 0.1, minScale);
    }
  };

  const handleRefresh = async () => {
    loading.value = true;
    try {
      await renderPreview();
      emit('refresh');
      EleMessage.success('预览已刷新');
    } catch (error) {
      EleMessage.error('刷新预览失败: ' + error.message);
    } finally {
      loading.value = false;
    }
  };

  const handlePrint = async () => {
    if (!paginatedTemplates.value.length) {
      EleMessage.error('没有可打印的数据，请先刷新预览');
      return;
    }

    printing.value = true;
    try {
      // 收集所有页面的HTML内容
      let allPagesHtml = '';

      paginatedTemplates.value.forEach((page, pageIndex) => {
        let pageContent = '';

        // 收集当前页面所有标签的内容
        page.forEach((item, labelIndex) => {
          const container = document.getElementById(
            `hiprint-preview-container-${item.originalIndex}`
          );
          if (container && container.innerHTML.trim()) {
            // 应用与预览相同的标签样式
            const labelStyle = getLabelItemStyle(labelIndex);
            const styleString = Object.entries(labelStyle)
              .map(
                ([key, value]) =>
                  `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`
              )
              .join('; ');

            pageContent += `
            <div class="label-item" style="${styleString}; break-inside: avoid;">
              <div class="hiprint-preview-container" style="
                min-height: 60px;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: flex-start;
                padding: 10px;
              ">
                ${container.innerHTML}
              </div>
            </div>
          `;
          }
        });

        if (pageContent) {
          // 应用与预览相同的页面样式
          const pageStyle = getPrintPageStyle(pageIndex);
          const pageStyleString = Object.entries(pageStyle)
            .filter(([key]) => !['boxShadow', 'borderRadius'].includes(key)) // 移除预览专用样式
            .map(
              ([key, value]) =>
                `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`
            )
            .join('; ');

          allPagesHtml += `
          <div class="print-page" style="${pageStyleString}; page-break-after: ${pageIndex < paginatedTemplates.value.length - 1 ? 'always' : 'auto'};">
            <div class="labels-container" style="
              flex: 1;
              display: flex;
              flex-direction: column;
            ">
              ${pageContent}
            </div>
          </div>
        `;
        }
      });

      if (!allPagesHtml) {
        throw new Error('没有找到可打印的内容');
      }

      // 获取打印底纸尺寸
      const paperWidth = paperSettings.value?.paperWidth || 210; // 默认A4宽度(mm)
      const paperHeight = paperSettings.value?.paperHeight || 297; // 默认A4高度(mm)

      // 获取hiprint相关的CSS样式
      const hiprintStyles = getHiprintStyles();

      // 打开新窗口进行打印
      const printWindow = window.open('', '_blank');
      const printHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>批量标签打印 - 共${paginatedTemplates.value.length}页</title>
        <style>
          /* 基础样式 */
          body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: white;
          }

          /* 页面容器样式 */
          .print-page {
            position: relative;
            display: flex;
            flex-direction: column;
            background: white;
          }

          /* 标签容器样式 */
          .labels-container {
            flex: 1;
            display: flex;
            flex-direction: column;
          }

          /* 标签项样式 */
          .label-item {
            flex-shrink: 0;
            break-inside: avoid;
            position: relative;
          }

          /* hiprint容器样式 */
          .hiprint-preview-container {
            min-height: 60px;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 10px;
          }

          /* hiprint生成的内容样式 */
          .hiprint-printPanel {
            background: white;
            overflow: hidden;
            transform-origin: top center;
            max-width: 100%;
            height: auto;
          }

          .hiprint-printElement {
            box-sizing: border-box;
          }

          .hiprint-printElement-text {
            word-break: break-word;
            overflow-wrap: break-word;
          }

          .hiprint-printElement-qrcode {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .hiprint-printElement-qrcode img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }

          /* 打印媒体查询 */
          @media print {
            body {
              padding: 0;
              margin: 0;
              background: white;
            }

            .print-page {
              page-break-after: always;
              margin: 0 !important;
              padding: ${pageConfig.value.pageMargin}px !important;
              width: ${paperWidth}mm !important;
              height: ${paperHeight}mm !important;
              box-shadow: none !important;
              border-radius: 0 !important;
              background: white !important;
              position: relative !important;
              display: flex !important;
              flex-direction: column !important;
              overflow: hidden !important;
            }

            .print-page:last-child {
              page-break-after: auto;
            }

            .labels-container {
              flex: 1 !important;
              display: flex !important;
              flex-direction: column !important;
            }

            .label-item {
              break-inside: avoid !important;
              margin-bottom: ${pageConfig.value.labelSpacing}px !important;
              position: relative !important;
            }

            .hiprint-preview-container {
              min-height: 60px !important;
              width: 100% !important;
              display: flex !important;
              justify-content: center !important;
              align-items: flex-start !important;
              padding: 10px !important;
            }

            .page-header {
              display: none !important;
            }

            @page {
              size: ${paperWidth}mm ${paperHeight}mm;
              margin: 0;
            }
          }

          /* 屏幕预览样式 */
          @media screen {
            .print-page {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              margin-bottom: 20px;
              border-radius: 8px;
            }
          }

          /* hiprint相关样式 */
          ${hiprintStyles}
        </style>
      </head>
      <body>
        ${allPagesHtml}
      </body>
      </html>
    `;

      printWindow.document.write(printHTML);
      printWindow.document.close();

      // 调试信息
      console.log('打印页面数量:', paginatedTemplates.value.length);
      console.log('打印底纸尺寸:', `${paperWidth}mm × ${paperHeight}mm`);
      console.log('页面配置:', pageConfig.value);

      // 等待样式加载完成后再打印
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          // 注释掉自动关闭，方便调试
          // printWindow.close();
        }, 500); // 延迟500ms确保样式完全加载
      };

      emit('confirm-print', {
        ...props.printData,
        action: 'print'
      });

      // EleMessage.success(`打印任务已发送 (共${paginatedTemplates.value.length}页)`);
    } catch (error) {
      EleMessage.error('打印失败：' + error.message);
    } finally {
      printing.value = false;
    }
  };

  const handlePrintPreview = async () => {
    if (!paginatedTemplates.value.length) {
      EleMessage.error('没有可预览的模板数据，请先刷新预览');
      return;
    }

    try {
      // 生成打印HTML（与打印方法相同的逻辑，但不执行打印）
      let allPagesHtml = '';

      paginatedTemplates.value.forEach((page, pageIndex) => {
        let pageContent = '';

        page.forEach((template, labelIndex) => {
          const container = document.getElementById(
            `hiprint-preview-container-${template.originalIndex}`
          );
          if (container && container.innerHTML.trim()) {
            const labelStyle = getLabelItemStyle(labelIndex);
            const styleString = Object.entries(labelStyle)
              .map(
                ([key, value]) =>
                  `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`
              )
              .join('; ');

            pageContent += `
            <div class="label-item" style="${styleString}; break-inside: avoid;">
              <div class="hiprint-preview-container" style="
                min-height: 60px;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: flex-start;
                padding: 10px;
              ">
                ${container.innerHTML}
              </div>
            </div>
          `;
          }
        });

        if (pageContent) {
          const pageStyle = getPrintPageStyle(pageIndex);
          const pageStyleString = Object.entries(pageStyle)
            .filter(([key]) => !['boxShadow', 'borderRadius'].includes(key))
            .map(
              ([key, value]) =>
                `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`
            )
            .join('; ');

          allPagesHtml += `
          <div class="print-page" style="${pageStyleString}; page-break-after: ${pageIndex < paginatedTemplates.value.length - 1 ? 'always' : 'auto'};">
            <div class="labels-container" style="
              flex: 1;
              display: flex;
              flex-direction: column;
            ">
              ${pageContent}
            </div>
          </div>
        `;
        }
      });

      const paperWidth = paperSettings.value?.paperWidth || 210;
      const paperHeight = paperSettings.value?.paperHeight || 297;
      const hiprintStyles = getHiprintStyles();

      // 打开新窗口显示打印预览（不执行打印）
      const previewWindow = window.open('', '_blank');
      const previewHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>打印预览 - 共${paginatedTemplates.value.length}页</title>
        <style>
          body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
          }

          .preview-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          }

          .print-page {
            position: relative;
            display: flex;
            flex-direction: column;
            background: white;
            margin: 0 auto 20px auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            width: ${paperWidth}mm;
            min-height: ${paperHeight}mm;
            padding: ${pageConfig.value.pageMargin}px;
            overflow: hidden;
          }

          .labels-container {
            flex: 1;
            display: flex;
            flex-direction: column;
          }

          .label-item {
            flex-shrink: 0;
            break-inside: avoid;
            position: relative;
          }

          .hiprint-preview-container {
            min-height: 60px;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 10px;
          }

          .hiprint-printPanel {
            background: white;
            overflow: hidden;
            transform-origin: top center;
            max-width: 100%;
            height: auto;
          }

          .hiprint-printElement {
            box-sizing: border-box;
          }

          .hiprint-printElement-text {
            word-break: break-word;
            overflow-wrap: break-word;
          }

          .hiprint-printElement-qrcode {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .hiprint-printElement-qrcode img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }

          ${hiprintStyles}
        </style>
      </head>
      <body>
        <div class="preview-header">
          <h2>打印预览</h2>
          <p>共 ${paginatedTemplates.value.length} 页 | 纸张尺寸: ${paperWidth}mm × ${paperHeight}mm</p>
          <p style="color: #666; font-size: 14px;">此预览显示的布局与实际打印效果一致</p>
        </div>
        ${allPagesHtml}
      </body>
      </html>
    `;

      previewWindow.document.write(previewHTML);
      previewWindow.document.close();

      EleMessage.success('打印预览已打开');
    } catch (error) {
      EleMessage.error('打印预览失败：' + error.message);
    }
  };

  const handleDownload = async () => {
    if (!props.printData?.processedTemplates?.length) {
      EleMessage.error('没有可下载的模板数据，请先刷新预览');
      return;
    }

    downloading.value = true;
    try {
      // 通过后端API下载PDF
      emit('confirm-print', {
        ...props.printData,
        action: 'download'
      });

      EleMessage.success('PDF下载已开始');
    } catch (error) {
      EleMessage.error('下载失败：' + error.message);
    } finally {
      downloading.value = false;
    }
  };

  // 监听visible变化，重置缩放比例并渲染预览
  watch(visible, async (newVal) => {
    if (newVal) {
      scale.value = 1;
      // 延迟一点时间确保DOM渲染完成
      await nextTick();
      setTimeout(async () => {
        try {
          await renderPreview();
        } catch (error) {
          console.error('自动渲染预览失败:', error);
        }
      }, 100);
    }
  });

  // 暴露方法供外部调用
  defineExpose({
    handleRefresh,
    handleZoomIn,
    handleZoomOut,
    handlePrint,
    handlePrintPreview,
    getPrintPageStyle,
    getLabelItemStyle,
    paginatedTemplates
  });
</script>

<style scoped>
  .enhanced-preview-drawer {
    /* 抽屉样式 */
  }

  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 16px;
  }

  .drawer-title-section {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .drawer-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .drawer-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .print-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
  }

  .print-info-bar {
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  .info-details {
    display: flex;
    gap: 16px;
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }

  .preview-area {
    flex: 1;
    overflow: auto;
    background: #f0f2f5;
    border-radius: 8px;
    min-height: 400px;
  }

  .print-page {
    position: relative;
    display: flex;
    flex-direction: column;
  }

  /* 打印纸阴影效果 */
  .print-page::before {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 8px;
    right: 8px;
    height: 8px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);
    border-radius: 0 0 6px 6px;
    z-index: -1;
  }

  .print-page::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 16px;
    right: 16px;
    height: 8px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
    border-radius: 0 0 4px 4px;
    z-index: -2;
  }

  /* 页面头部样式 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
    color: #666;
    flex-shrink: 0;
  }

  .page-number {
    font-weight: 600;
    color: #409eff;
  }

  .page-info {
    color: #909399;
  }

  /* 标签容器样式 */
  .labels-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .label-item {
    flex-shrink: 0;
    break-inside: avoid;
  }

  .hiprint-preview-container {
    min-height: 60px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 10px;
  }

  /* hiprint生成的内容样式 */
  .hiprint-preview-container :deep(.hiprint-printPanel) {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
    transform-origin: top center;
    max-width: 100%;
    height: auto;
  }

  .hiprint-preview-container :deep(.hiprint-printElement) {
    box-sizing: border-box;
  }

  .hiprint-preview-container :deep(.hiprint-printElement-text) {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .hiprint-preview-container :deep(.hiprint-printElement-qrcode) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hiprint-preview-container :deep(.hiprint-printElement-qrcode img) {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
</style>
