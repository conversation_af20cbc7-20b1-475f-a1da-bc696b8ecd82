-- 修复重复送评条码的SQL脚本
-- 作者: Payne
-- 日期: 2024-08-01
-- 说明: 此脚本用于检查和清理PJ_O_SENDFORM_ITEM表中重复的DIY_CODE数据

-- 1. 查询重复的送评条码
SELECT 
    DIY_CODE,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(ID) as duplicate_ids,
    GROUP_CONCAT(SENDNUM) as sendnums
FROM PJ_O_SENDFORM_ITEM 
WHERE DIY_CODE IS NOT NULL 
  AND DIY_CODE != ''
GROUP BY DIY_CODE 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. 查看具体的重复记录详情
SELECT 
    ID,
    SENDNUM,
    DIY_CODE,
    COIN_NAME1,
    COIN_TYPE,
    SERIAL_NUMBER,
    SEQNO,
    INUPTTIME,
    UPDATETIME
FROM PJ_O_SENDFORM_ITEM 
WHERE DIY_CODE IN (
    SELECT DIY_CODE 
    FROM PJ_O_SENDFORM_ITEM 
    WHERE DIY_CODE IS NOT NULL 
      AND DIY_CODE != ''
    GROUP BY DIY_CODE 
    HAVING COUNT(*) > 1
)
ORDER BY DIY_CODE, INUPTTIME DESC;

-- 3. 创建临时表保存要保留的记录（每个DIY_CODE保留最新的一条）
CREATE TEMPORARY TABLE temp_keep_records AS
SELECT 
    t1.ID,
    t1.DIY_CODE
FROM PJ_O_SENDFORM_ITEM t1
INNER JOIN (
    SELECT 
        DIY_CODE,
        MAX(COALESCE(UPDATETIME, INUPTTIME)) as latest_time
    FROM PJ_O_SENDFORM_ITEM 
    WHERE DIY_CODE IS NOT NULL 
      AND DIY_CODE != ''
    GROUP BY DIY_CODE
) t2 ON t1.DIY_CODE = t2.DIY_CODE 
    AND COALESCE(t1.UPDATETIME, t1.INUPTTIME) = t2.latest_time;

-- 4. 查看将要删除的重复记录
SELECT 
    ID,
    SENDNUM,
    DIY_CODE,
    COIN_NAME1,
    COIN_TYPE,
    SERIAL_NUMBER,
    INUPTTIME,
    UPDATETIME,
    '将被删除' as action
FROM PJ_O_SENDFORM_ITEM 
WHERE DIY_CODE IS NOT NULL 
  AND DIY_CODE != ''
  AND ID NOT IN (SELECT ID FROM temp_keep_records)
  AND DIY_CODE IN (
    SELECT DIY_CODE 
    FROM PJ_O_SENDFORM_ITEM 
    WHERE DIY_CODE IS NOT NULL 
      AND DIY_CODE != ''
    GROUP BY DIY_CODE 
    HAVING COUNT(*) > 1
  )
ORDER BY DIY_CODE, INUPTTIME DESC;

-- 5. 备份重复记录到备份表（可选）
CREATE TABLE PJ_O_SENDFORM_ITEM_BACKUP_DUPLICATES AS
SELECT 
    *,
    NOW() as backup_time,
    '重复DIY_CODE备份' as backup_reason
FROM PJ_O_SENDFORM_ITEM 
WHERE DIY_CODE IS NOT NULL 
  AND DIY_CODE != ''
  AND ID NOT IN (SELECT ID FROM temp_keep_records)
  AND DIY_CODE IN (
    SELECT DIY_CODE 
    FROM PJ_O_SENDFORM_ITEM 
    WHERE DIY_CODE IS NOT NULL 
      AND DIY_CODE != ''
    GROUP BY DIY_CODE 
    HAVING COUNT(*) > 1
  );

-- 6. 删除重复记录（保留最新的一条）
-- 注意：执行此步骤前请确保已经备份数据
/*
DELETE FROM PJ_O_SENDFORM_ITEM 
WHERE DIY_CODE IS NOT NULL 
  AND DIY_CODE != ''
  AND ID NOT IN (SELECT ID FROM temp_keep_records)
  AND DIY_CODE IN (
    SELECT DIY_CODE 
    FROM (
        SELECT DIY_CODE 
        FROM PJ_O_SENDFORM_ITEM 
        WHERE DIY_CODE IS NOT NULL 
          AND DIY_CODE != ''
        GROUP BY DIY_CODE 
        HAVING COUNT(*) > 1
    ) as duplicate_codes
  );
*/

-- 7. 添加唯一索引防止将来出现重复（可选）
-- ALTER TABLE PJ_O_SENDFORM_ITEM ADD UNIQUE INDEX uk_diy_code (DIY_CODE);

-- 8. 验证清理结果
SELECT 
    'After cleanup' as status,
    COUNT(*) as total_records,
    COUNT(DISTINCT DIY_CODE) as unique_diy_codes,
    COUNT(*) - COUNT(DISTINCT DIY_CODE) as remaining_duplicates
FROM PJ_O_SENDFORM_ITEM 
WHERE DIY_CODE IS NOT NULL 
  AND DIY_CODE != '';

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_keep_records;

-- 使用说明：
-- 1. 首先运行查询语句（1-2）查看重复数据情况
-- 2. 如果需要备份，运行步骤5
-- 3. 取消注释步骤6的DELETE语句来删除重复记录
-- 4. 可选择添加唯一索引防止将来出现重复
-- 5. 运行步骤8验证清理结果
